# Generated by Django 4.2.20 on 2025-09-24 10:08

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0026_alter_subscriptioncustomer_portal_is_enabled'),
    ]

    operations = [
        migrations.AlterField(
            model_name='packagesubscriptionplan',
            name='package',
            field=models.CharField(choices=[('Basic', 'Basic'), ('Plus', 'Plus'), ('Freemium', 'Freemium'), ('Premium', 'Premium')], max_length=50),
        ),
        migrations.CreateModel(
            name='EmailNotificationLog',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer_id', models.CharField(max_length=50)),
                ('transaction_id', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('subscription_id', models.CharField(blank=True, max_length=50, null=True)),
                ('notification_type', models.CharField(choices=[('payment_success', 'Payment Success'), ('order_confirmation', 'Order Confirmation')], max_length=20)),
                ('recipient_email', models.EmailField(max_length=254)),
                ('subject', models.CharField(max_length=255)),
                ('sent', models.BooleanField(default=False)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'Email Notification Logs',
                'db_table': 'email_notification_logs',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['customer_id', 'notification_type'], name='email_notif_custome_3ae324_idx'), models.Index(fields=['transaction_id'], name='email_notif_transac_10120f_idx'), models.Index(fields=['subscription_id'], name='email_notif_subscri_8092c8_idx')],
            },
        ),
    ]
