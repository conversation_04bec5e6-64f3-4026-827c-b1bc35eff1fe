<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmed</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007cba; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .footer { padding: 20px; text-align: center; color: #666; }
        .button { display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px; }
        .order-details { background-color: white; padding: 15px; margin: 15px 0; border-left: 4px solid #007cba; }
        .item-list { list-style: none; padding: 0; }
        .item-list li { padding: 5px 0; border-bottom: 1px solid #eee; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Your order has been confirmed</h1>
        </div>
        
        <div class="content">
            <p>Hi {{ first_name }},</p>
            
            <p>Thank you for your order! 🎉 Your purchase has been successfully processed.</p>
            
            <div class="order-details">
                <h3>Order Details:</h3>
                <p><strong>Order Number:</strong> {{ order_number }}</p>
                <p><strong>Date:</strong> {{ order_date }}</p>
                {% if total_amount %}<p><strong>Total:</strong> {{ total_amount }}</p>{% endif %}
                
                <h4>Item(s):</h4>
                <ul class="item-list">
                    {% for item in order_items %}
                    <li>{{ item.name }}{% if item.plan_type %} ({{ item.plan_type }}){% endif %}</li>
                    {% endfor %}
                </ul>
            </div>
            
            <p>You can access your purchase immediately by logging into your account.</p>
            <p><a href="{{ account_link }}" class="button">Access Your Account</a></p>
            
            <p>Thanks for choosing Deviare!</p>
            
            <p>— The Deviare Team</p>
        </div>
        
        <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>