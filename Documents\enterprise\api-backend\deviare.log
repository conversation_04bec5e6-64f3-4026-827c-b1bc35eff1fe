
 INFO 2025-09-24 21:58:26,712 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-09-24 21:59:09,434 views 17073 Fetching transactions for customer from API: 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 21:59:11,473 views 17078 Fetching subscriptions for customer from API: 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 21:59:13,924 views 17120 Found 4 subscriptions for customer 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 21:59:13,925 views 17122 Subscription 1: status='Active', payment_status='Pending', display_name='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase' 


 INFO 2025-09-24 21:59:13,925 views 17122 Subscription 2: status='Active', payment_status='Paid', display_name='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase' 


 INFO 2025-09-24 21:59:13,926 views 17122 Subscription 3: status='Active', payment_status='Paid', display_name='Introduction to AWS Management Console -> Freemium' 


 INFO 2025-09-24 21:59:13,927 views 17122 Subscription 4: status='Active', payment_status='Pending', display_name='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase' 


 INFO 2025-09-24 21:59:13,927 views 17123 Has confirmed subscription: True 


 INFO 2025-09-24 21:59:20,874 views 17142 Processing 4 transactions for customer 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 21:59:20,874 views 17165 Transaction 1a28e32a-6ca8-4b38-9bcb-0c5deb78f635: status='Pending', description='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase', normalized_title='aws artificial intelligence practitioner learning plan -> lifetime purchase', matched=True 


 INFO 2025-09-24 21:59:20,875 views 17205 Skipping payment email for transaction 1a28e32a-6ca8-4b38-9bcb-0c5deb78f635: is_confirmed=True, status=Pending 


 INFO 2025-09-24 21:59:20,875 views 17165 Transaction 87b4ba1d-328e-47ea-a2e7-26c9f91a3144: status='Failed', description='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase', normalized_title='aws artificial intelligence practitioner learning plan -> lifetime purchase', matched=True 


 INFO 2025-09-24 21:59:20,875 views 17205 Skipping payment email for transaction 87b4ba1d-328e-47ea-a2e7-26c9f91a3144: is_confirmed=True, status=Failed 


 INFO 2025-09-24 21:59:20,876 views 17165 Transaction c5aca4bc-0bd4-4f97-9e67-65647285a948: status='Failed', description='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase', normalized_title='aws artificial intelligence practitioner learning plan -> lifetime purchase', matched=True 


 INFO 2025-09-24 21:59:20,876 views 17205 Skipping payment email for transaction c5aca4bc-0bd4-4f97-9e67-65647285a948: is_confirmed=True, status=Failed 


 INFO 2025-09-24 21:59:20,876 views 17165 Transaction d9dd970e-b437-437f-ad3f-dc8eb6b85d5c: status='Paid', description='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase', normalized_title='aws artificial intelligence practitioner learning plan -> lifetime purchase', matched=True 


 INFO 2025-09-24 21:59:20,877 views 17196 Sending payment success email for transaction d9dd970e-b437-437f-ad3f-dc8eb6b85d5c for customer 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 21:59:32,215 email_notifications 122 Payment success email <NAME_EMAIL> for transaction d9dd970e-b437-437f-ad3f-dc8eb6b85d5c 


 INFO 2025-09-24 21:59:32,217 views 17203 Payment success email result: True 


 INFO 2025-09-24 21:59:32,218 views 17209 Fetched 4 transactions and 4 subscriptions for customer 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 21:59:32,218 views 17213 Sending order confirmation email for customer 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 with 2 confirmed subscriptions 


 ERROR 2025-09-24 21:59:34,294 email_notifications 222 Error sending order confirmation email: 'UUID' object is not subscriptable 


 INFO 2025-09-24 21:59:34,294 views 17216 Order confirmation email result: False 


 INFO 2025-09-24 22:01:37,285 views 17073 Fetching transactions for customer from API: 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 22:01:40,653 views 17078 Fetching subscriptions for customer from API: 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 22:01:43,044 views 17120 Found 4 subscriptions for customer 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 22:01:43,044 views 17122 Subscription 1: status='Active', payment_status='Pending', display_name='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase' 


 INFO 2025-09-24 22:01:43,044 views 17122 Subscription 2: status='Active', payment_status='Paid', display_name='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase' 


 INFO 2025-09-24 22:01:43,044 views 17122 Subscription 3: status='Active', payment_status='Paid', display_name='Introduction to AWS Management Console -> Freemium' 


 INFO 2025-09-24 22:01:43,044 views 17122 Subscription 4: status='Active', payment_status='Pending', display_name='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase' 


 INFO 2025-09-24 22:01:43,045 views 17123 Has confirmed subscription: True 


 INFO 2025-09-24 22:01:49,350 views 17142 Processing 4 transactions for customer 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 22:01:49,351 views 17165 Transaction 1a28e32a-6ca8-4b38-9bcb-0c5deb78f635: status='Pending', description='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase', normalized_title='aws artificial intelligence practitioner learning plan -> lifetime purchase', matched=True 


 INFO 2025-09-24 22:01:49,351 views 17205 Skipping payment email for transaction 1a28e32a-6ca8-4b38-9bcb-0c5deb78f635: is_confirmed=True, status=Pending 


 INFO 2025-09-24 22:01:49,352 views 17165 Transaction 87b4ba1d-328e-47ea-a2e7-26c9f91a3144: status='Failed', description='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase', normalized_title='aws artificial intelligence practitioner learning plan -> lifetime purchase', matched=True 


 INFO 2025-09-24 22:01:49,352 views 17205 Skipping payment email for transaction 87b4ba1d-328e-47ea-a2e7-26c9f91a3144: is_confirmed=True, status=Failed 


 INFO 2025-09-24 22:01:49,352 views 17165 Transaction c5aca4bc-0bd4-4f97-9e67-65647285a948: status='Failed', description='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase', normalized_title='aws artificial intelligence practitioner learning plan -> lifetime purchase', matched=True 


 INFO 2025-09-24 22:01:49,352 views 17205 Skipping payment email for transaction c5aca4bc-0bd4-4f97-9e67-65647285a948: is_confirmed=True, status=Failed 


 INFO 2025-09-24 22:01:49,352 views 17165 Transaction d9dd970e-b437-437f-ad3f-dc8eb6b85d5c: status='Paid', description='AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase', normalized_title='aws artificial intelligence practitioner learning plan -> lifetime purchase', matched=True 


 INFO 2025-09-24 22:01:49,352 views 17196 Sending payment success email for transaction d9dd970e-b437-437f-ad3f-dc8eb6b85d5c for customer 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 22:01:50,324 email_notifications 76 Payment success email already sent for transaction d9dd970e-b437-437f-ad3f-dc8eb6b85d5c 


 INFO 2025-09-24 22:01:50,324 views 17203 Payment success email result: True 


 INFO 2025-09-24 22:01:50,325 views 17209 Fetched 4 transactions and 4 subscriptions for customer 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 


 INFO 2025-09-24 22:01:50,325 views 17213 Sending order confirmation email for customer 26469e57-9cfa-42dc-8ea3-32d80ecb0c74 with 2 confirmed subscriptions 


 ERROR 2025-09-24 22:01:53,258 email_notifications 222 Error sending order confirmation email: 'UUID' object is not subscriptable 


 INFO 2025-09-24 22:01:53,259 views 17216 Order confirmation email result: False 


 INFO 2025-09-24 22:02:55,571 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\email_notifications.py changed, reloading. 

