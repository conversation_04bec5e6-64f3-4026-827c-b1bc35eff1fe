import requests
import csv
from datetime import datetime
import time

# ======================
# STEP 1: Configuration
# ======================
KEYCLOAK_URL = "https://identity.deviare.africa/auth"
REALM = "Deviare"
USERNAME = "<EMAIL>"   # admin username
PASSWORD = "LatestPassword@!@#"          # admin password
CLIENT_ID = "admin-cli"

# Date range for filtering (DD-MM-YYYY)
DATE_FROM = "01-09-2024"
DATE_TO = "15-09-2024"

# Convert to epoch millis
date_from_epoch = int(datetime.strptime(DATE_FROM, "%d-%m-%Y").timestamp() * 1000)
date_to_epoch = int(datetime.strptime(DATE_TO, "%d-%m-%Y").timestamp() * 1000)

# ======================
# STEP 2: Get Access Token
# ======================
def get_access_token():
    url = f"{KEYCLOAK_URL}/realms/master/protocol/openid-connect/token"
    payload = {
        "client_id": CLIENT_ID,
        "username": USERNAME,
        "password": PASSWORD,
        "grant_type": "password"
    }
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    
    response = requests.post(url, data=payload, headers=headers)
    
    if response.status_code != 200:
        raise Exception(f"Failed to get token: {response.text}")
        
    return response.json()["access_token"]

# ======================
# STEP 3: Fetch Users (Paginated)
# ======================
def get_users():
    global access_token
    users = []
    first = 0
    max_results = 100
    
    while True:
        headers = {"Authorization": f"Bearer {access_token}"}
        url = f"{KEYCLOAK_URL}/admin/realms/{REALM}/users"
        params = {"first": first, "max": max_results}
        response = requests.get(url, headers=headers, params=params)
        
        # Handle token expiration
        if response.status_code == 401:
            print("Token expired, refreshing...")
            access_token = get_access_token()
            headers = {"Authorization": f"Bearer {access_token}"}
            response = requests.get(url, headers=headers, params=params)
        
        if response.status_code != 200:
            print(f"Error at offset {first}: {response.status_code} - {response.text}")
            break
        
        batch = response.json()
        if not batch:
            break
            
        users.extend(batch)
        first += max_results
        
        print(f"Fetched {len(users)} users so far...")
        time.sleep(0.1)
    
    return users

# ======================
# STEP 4: Get Last Login for a User
# ======================
def get_last_login(user_id):
    global access_token
    headers = {"Authorization": f"Bearer {access_token}"}
    url = f"{KEYCLOAK_URL}/admin/realms/{REALM}/events"
    params = {
        "type": "LOGIN",
        "user": user_id,
        "dateFrom": date_from_epoch,
        "dateTo": date_to_epoch
    }
    response = requests.get(url, headers=headers, params=params)
    if response.status_code == 200:
        events = response.json()
        if events:
            # Sort by time (descending)
            events.sort(key=lambda e: e.get("time", 0), reverse=True)
            last_time = events[0]["time"]
            return datetime.fromtimestamp(last_time / 1000).strftime("%Y-%m-%d %H:%M:%S")
    return None

# ======================
# STEP 5: Main Execution
# ======================
access_token = get_access_token()
users = get_users()
print(f"Total users fetched: {len(users)}")

filename = f"keycloak_users_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

with open(filename, "w", newline="") as csvfile:
    fieldnames = ["id", "username", "email", "enabled", "lastLogin"]
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    writer.writeheader()

    for user in users:
        last_login = get_last_login(user["id"])
        writer.writerow({
            "id": user["id"],
            "username": user.get("username", ""),
            "email": user.get("email", ""),
            "enabled": user.get("enabled", ""),
            "lastLogin": last_login or ""
        })

print(f"✅ CSV saved as {filename}")
