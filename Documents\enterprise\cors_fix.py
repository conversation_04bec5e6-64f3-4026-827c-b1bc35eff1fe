# Add this to your Django settings.py

# Install django-cors-headers first:
# pip install django-cors-headers

# 1. Add to INSTALLED_APPS
INSTALLED_APPS = [
    # ... your other apps
    'corsheaders',
]

# 2. Add to MIDDLEWARE (at the top)
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    # ... your other middleware
]

# 3. Configure CORS settings
# For development (allows localhost:3000)
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

# Or for development only, you can use (less secure):
# CORS_ALLOW_ALL_ORIGINS = True

# Allow specific headers
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'sec-ch-ua',
    'sec-ch-ua-mobile',
    'sec-ch-ua-platform',
]

# Allow credentials if needed
CORS_ALLOW_CREDENTIALS = True