from .settings_base import *
from celery.schedules import crontab
import os
from dotenv import load_dotenv

load_dotenv()

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
SECRET_KEY = os.getenv('SECRET_KEY')  #or "dummy"
HTTP_STATIC_TOKEN = os.getenv('HTTP_STATIC_TOKEN')
ALLOWED_HOSTS = ["*"]
SECURE_SSL_REDIRECT=False
#SESSION_COOKIE_SECURE=True
#CSRF_COOKIE_SECURE=True

REDIS_HOST = os.getenv('REDIS_HOST')

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_extensions",
    "rest_framework",
    "django_celery_beat",
    "rest_framework.authtoken",
    "corsheaders",
    "storages",
    "main.apps.MainConfig",
    "project.apps.ProjectConfig",
    "services.apps.ServicesConfig",
    "django_crontab",
    'lms.apps.LmsConfig',
    'wp_api.apps.WpApiConfig',
    'notification',
    'badge',
    "sslserver",
    'drf_yasg',
    'django_celery_results',
    "celery_tasks_api"
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'deviare.urls'

# CORS Configuration
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_HEADERS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

# Add these CORS settings to properly handle all required headers
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'x-forwarded-for',
    'referer',
]

CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

CORS_PREFLIGHT_MAX_AGE = 86400

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]
SESSION_SERIALIZER = 'tools.data_serializers.UjsonSerializer'
SESSION_ENGINE = 'redis_sessions.session'
SESSION_REDIS = {
    'host': REDIS_HOST,
    'port': 6379,
    'db': 1,
    'password': '',
    'prefix': 'session',
    'socket_timeout': 1,
    'retry_on_timeout': False
    }

WSGI_APPLICATION = 'deviare.wsgi.application'

# Password validation
# https://docs.djangoproject.com/en/1.9/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/1.9/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/1.9/howto/static-files/

STATIC_URL = '/static/'

STATICFILES_DIRS = (os.path.join(BASE_DIR, 'static'),)

STATIC_ROOT = BASE_DIR

ADMIN_MEDIA_PREFIX = "/static/admin/"

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.BasicAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ),
    "DEFAULT_PAGINATION_CLASS": "deviare.pagination.CustomPageNumberPagination",
    "PAGE_SIZE": 10,

}
DEBUG = False
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.getenv('DB_NAME'),
        'USER': os.getenv('DB_USER'),
        'PASSWORD': os.getenv('DB_PASSWORD'),
        'HOST': os.getenv('DB_HOST'),
        'PORT': os.getenv('DB_PORT'),
        'OPTIONS': {
            'auth_plugin': 'caching_sha2_password',
            'charset': 'utf8mb4',
        },
    },
    'badge': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.getenv('BADGE_DB_NAME'),
        'USER': os.getenv('BADGE_DB_USER'),
        'PASSWORD': os.getenv('BADGE_DB_PASSWORD'),
        "HOST": os.getenv('BADGE_DB_HOST'),
        'PORT': os.getenv('BADGE_DB_PORT'),
    }
}
DEPLOYMENT_ENV = 'testing'
COMPANY_NAME = None
KEYCLOAK_URL = os.getenv('KEYCLOAK_URL')
KEYCLOAK_REALM = os.getenv('KEYCLOAK_REALM')
KEYCLOAK_CLIENTID = os.getenv('KEYCLOAK_CLIENTID')
KEYCLOAK_CLIENTSECRET = os.getenv('KEYCLOAK_CLIENTSECRET')
KEYCLOAK_ADMINUSER = os.getenv('KEYCLOAK_ADMINUSER')
KEYCLOAK_ADMINPASSWORD = os.getenv('KEYCLOAK_ADMINPASSWORD')
KEYCLOAK_DOMAIN = os.getenv('KEYCLOAK_DOMAIN')
KEYCLOAK_REALM = os.getenv('KEYCLOAK_REALM');

KEYCLOAK_ISSUER = os.getenv('KEYCLOAK_ISSUER');

PROXY_URL=os.getenv('PROXY_URL')
PROXY_CONFIG_PATH=os.getenv('PROXY_CONFIG_PATH')
PROXY_APACHE_PATH=os.getenv('PROXY_APACHE_PATH')

URL=os.getenv('URL')

EMAIL_HOST = os.getenv('EMAIL_HOST')
EMAIL_SUBJECT_PREFIX = 'Deviare Enterprise Platform'

EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD')
EMAIL_PORT = 587
EMAIL_USE_TLS = True

CLIENT_ID = os.getenv('CLIENT_ID')
CLIENT_SECRET = os.getenv('CLIENT_SECRET')
AUTHORIZATION_BASE_URL = os.getenv('AUTHORIZATION_BASE_URL')
TOKEN_URL = os.getenv('TOKEN_URL')

SALES_EMAILS = os.getenv("SALES_EMAILS")

PRACTICE_LABS_SERVER_URL = os.getenv('PRACTICE_LABS_SERVER_URL')
PRACTICE_LABS_USERNAME = os.getenv('PRACTICE_LABS_USERNAME')
PRACTICE_LABS_PASSWORD = os.getenv('PRACTICE_LABS_PASSWORD')
PRACTICE_LABS_DOMAIN = os.getenv('PRACTICE_LABS_DOMAIN')

deviare_config = {
    "AWS_S3_ACCESS_KEY":os.getenv('AWS_S3_ACCESS_KEY'),
    "AWS_S3_SECRET_KEY":os.getenv('AWS_S3_SECRET_KEY'),
    "AWS_S3_BUCKET_NAME":os.getenv('AWS_S3_BUCKET_NAME'),
    "SENDER_ADDRESS":"<EMAIL>",
    "MAIL_SUBJECT": "Test1",
    "EXCEL_NAME":"test",
    'CLIENT_ID':os.getenv('CONFIG_CLIENT_ID'),
    'CLIENT_SECRET':os.getenv('CONFIG_CLIENT_SECRET'),
    'MS_AUTHORITY':os.getenv('CONFIG_MS_AUTHORITY'),
    'MS_GRAPH_POINT': os.getenv('CONFIG_MS_GRAPH_POINT'),
    'MS_OAUTH_REDIRECT_URL': os.getenv('CONFIG_MS_OAUTH_REDIRECT_URL')
}


# Celery Configuration

CELERY_BROKER_URL = 'redis://%s:6379/0' % REDIS_HOST
# CELERY_RESULT_BACKEND = 'redis://%s:6379/0' % REDIS_HOST
CELERY_RESULT_BACKEND = 'django-db'
CELERY_BEAT_SCHEDULER = 'deviare.custom_scheduler.CustomDatabaseScheduler'
CELERY_TIMEZONE = 'UTC'
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TASK_SERIALIZER = 'json'
CELERY_IMPORTS = ["main.tasks",]
CLOUD_FRONT_DISTRIBUTION_ID = os.getenv('CLOUD_FRONT_DISTRIBUTION_ID')
CLOUD_FRONT_DISTRIBUTION_ID_STRATEGIC = os.getenv('CLOUD_FRONT_DISTRIBUTION_ID_STRATEGIC')
CLOUD_FRONT_DISTRIBUTION_NAME_STRATEGIC = os.getenv('CLOUD_FRONT_DISTRIBUTION_NAME_STRATEGIC')
CLOUD_FRONT_DISTRIBUTION_NAME = os.getenv('CLOUD_FRONT_DISTRIBUTION_NAME')
ROUTE53_LMS_WHITELABELING_INSTANCE = os.getenv('ROUTE53_LMS_WHITELABELING_INSTANCE')
ROUTE53_XPERTSKILLS_WHITELABELING_INSTANCE = os.getenv('ROUTE53_XPERTSKILLS_WHITELABELING_INSTANCE')


CELERY_BEAT_SCHEDULE = {
    'update-course-content': {
        'task': 'get_all_courses_from_talent_lms',
        'schedule': crontab(minute=30, hour='*/1'),  # every 1 hours at HH:30
    },
    'update-course-from-xpert-skills': {
        'task': 'get_all_courses_from_xpert_skills',
        'schedule': crontab(minute=0, hour='*/11'),  # every 11 hours at HH:00
    },
    'update-user-xpertskills-report': {
        'task': 'get_all_user_reports_from_xpert_skills',
        'schedule': crontab(minute=30, hour='*/9'),  # every 9 hours at HH:30
    },
    'sync-valueed-calender-events': {
        'task': 'sync_valueed_calender_events',
        'schedule': crontab(minute=0, hour='*/7'),  # every 7 hours at HH:00
    },
    'update-course-content-for-report': {
        'task': 'download_report_from_simplelearn',
        'schedule': crontab(minute=30, hour='*/5'),  # every 5 hours at HH:30
    },
    'devaire_update-course-content-for-report': {
        'task': 'deviare_download_report_from_simplelearn',
        'schedule': crontab(minute=0, hour='0,6,12,18'),  # every 6 hours at start of these hours
    },
    'run_email_queue': {
        'task': 'run_email_queue',
        'schedule': crontab(minute='0,30'),  # every 30 minutes (at HH:00 and HH:30) to avoid overlap and reduce load
    },
      'fetch-all-gcindex-detail': {
        'task': 'fetch_all_gcindex_detail',
        'schedule': crontab(minute=30, hour=6),  # Run once daily at 6:30 AM
    },
    'fetch-all-gcindex-detail-after': {
        'task': 'fetch_all_gcindex_detail',
        'schedule': crontab(minute=30, hour=5),  # Run once daily at 5:30 AM
    },
    'fetch-all-gcindex-url': {
        'task': 'fetch_all_gcindex_url',
        'schedule': crontab(minute=0, hour=5),  # Run once daily at 5 AM
    },
    'fetch-all-sparrow-response': {
        'task': 'fetch_all_sparrow_response',
        'schedule': crontab(minute=0, hour=6),  # Run once daily at 6 AM
    },
     # Updated tasks
    'get-all-courses-from-revinova': {
        'task': 'get_all_courses_from_revinova',
        'schedule': crontab(minute=0, hour='*/2'),  # Every 2 hours (e.g., 00:00, 12:00)
    },
    'get_learning_objects_progress': {
        'task': 'get_learning_objects_progress',
        'schedule': crontab(minute=30, hour='*/3'),  # offset by 30 min every 3 hours
    },
    'get_user_course_report_on_talent_lms': {
        'task': 'get_user_course_report_on_talent_lms',
        'schedule': crontab(minute=0, hour='0,12'),  # run twice a day at midnight and noon
    },
    'get_lime_survey_responses': {
        'task': 'get_lime_survey_responses',
        'schedule': crontab(minute=15, hour='0,8,16'),  # Every 8 hours at 00:15, 08:15, 16:15
    },
    'get_AI_lime_survey_responses': {
        'task': 'get_AI_lime_survey_responses',
        'schedule': crontab(minute=45, hour='0,10,20'),  # Every 10 hours at 00:45, 10:45, 20:45
    },
    'cleanup_old_task_results': {
        'task': 'cleanup_old_task_results',
        'schedule': crontab(hour=2, minute=0),  # every day at 2am
        'args': (1,),
    },
    'sync_subscription_transactions': {
        'task': 'sync_subscription_transactions',
        'schedule': crontab(hour=3, minute=0),  # every day at 3am
    },
    'sync_subscriptions': {
        'task': 'sync_subscriptions',
        'schedule': crontab(hour=1, minute=0),  # every day at 1am
    },
    'sync_invoices': {
        'task': 'sync_invoices',
        'schedule': crontab(hour=4, minute=0),  # every day at 4am
    },
    'sync_customers': {
        'task': 'sync_customers',
        'schedule': crontab(hour=5, minute=0),  # every day at 5am
    },
    'apply_grace_period_to_subscriptions': {
        'task': 'apply_grace_period_to_subscriptions',
        'schedule': crontab(hour=7, minute=0),  # every day at 7am
    },

}

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '\n {levelname} {asctime} {module} {lineno} {message} \n',
            'style': '{',
        }
    },
    'handlers':{
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },

        'toFile': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': './deviare.log',
            'when': 'midnight',
            'backupCount': 10,
            'formatter': 'verbose',
        }
    },
    'root': {
        'handlers': ['toFile'],
        'level': 'INFO'
    }
}
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME')
AWS_S3_REGION_NAME = 'us-west-2'
AWS_S3_FILE_OVERWRITE = False
AWS_DEFAULT_ACL = None
# DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'

SWAGGER_SETTINGS = {
   'SECURITY_DEFINITIONS': {
      'Basic': {
            'type': 'basic'
      },
      'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header'
      }
   }
}

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

TLMS_API_KEY = os.getenv('TLMS_API_KEY')
TLMS_URL_ADDRESS = os.getenv('TLMS_URL_ADDRESS')
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60

DTMM_SURVEY_ID = os.getenv('DTMM_SURVEY_ID')
AIMM_SURVEY_ID = os.getenv('AIMM_SURVEY_ID')
SURVEY_URL = os.getenv('SURVEY_URL')
LIME_SURVEY_URL = os.getenv('LIME_SURVEY_URL')

PROD_CLIENT_ID=os.getenv('PROD_CLIENT_ID')
PROD_CLIENT_SECRET=os.getenv('PROD_CLIENT_SECRET')
PROD_X_API_KEY=os.getenv('PROD_X_API_KEY')
PROD_EXTERNAL_API_URL=os.getenv('PROD_EXTERNAL_API_URL')

SENDGRID_API_KEY = os.getenv("SENDGRID_API_KEY")
