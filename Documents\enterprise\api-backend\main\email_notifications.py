from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils import timezone
from django.conf import settings
from .models import UserSettings, PackageSubscriptionPlan, EmailNotificationLog
import logging

logger = logging.getLogger(__name__)

class EmailNotificationService:
    """Service for sending order confirmation and payment success emails"""
    
    @staticmethod
    def get_user_details(customer_id):
        """Get user details from customer_id"""
        try:
            from .models import SubscriptionCustomer

            # First try to find customer in SubscriptionCustomer table
            subscription_customer = SubscriptionCustomer.objects.filter(id=customer_id).first()

            if subscription_customer:
                # Extract first name from full name
                name_parts = subscription_customer.name.split(' ', 1) if subscription_customer.name else ['Valued Customer']
                first_name = name_parts[0] if name_parts else 'Valued Customer'

                return {
                    'first_name': first_name,
                    'email': subscription_customer.email,
                    'full_name': subscription_customer.name or 'Valued Customer'
                }

            # Fallback: Try to find user by UUID in UserSettings
            user_settings = UserSettings.objects.filter(uuid=customer_id).first()

            if user_settings:
                return {
                    'first_name': user_settings.firstName or 'Valued Customer',
                    'email': user_settings.email,
                    'full_name': f"{user_settings.firstName} {user_settings.lastName}".strip() or 'Valued Customer'
                }

            # If not found anywhere, return default values
            logger.warning(f"No user found for customer_id: {customer_id}")
            return {
                'first_name': 'Valued Customer',
                'email': None,
                'full_name': 'Valued Customer'
            }

        except Exception as e:
            logger.error(f"Error getting user details for customer {customer_id}: {str(e)}")
            return {
                'first_name': 'Valued Customer',
                'email': None,
                'full_name': 'Valued Customer'
            }
    
    @staticmethod
    def send_payment_success_email(transaction_data, subscription_data, customer_id):
        """Send payment success email"""
        try:
            user_details = EmailNotificationService.get_user_details(customer_id)
            
            if not user_details['email']:
                logger.warning(f"No email found for customer {customer_id}")
                return False
            
            # Check if email already sent for this transaction
            if EmailNotificationLog.objects.filter(
                customer_id=customer_id,
                transaction_id=transaction_data.get('id'),
                notification_type='payment_success',
                sent=True
            ).exists():
                logger.info(f"Payment success email already sent for transaction {transaction_data.get('id')}")
                return True
            
            # Use full subscription display name (don't truncate)
            plan_name = subscription_data.get('subscription_display_name', 'Your Subscription')
            # Remove the -> splitting to show the full course name
            # plan_name now contains the full description like "AWS Artificial Intelligence Practitioner Learning Plan -> Lifetime Purchase"
            
            # Prepare email context
            context = {
                'first_name': user_details['first_name'],
                'amount': f"R{transaction_data.get('amount', 0)}",
                'plan_name': plan_name,
                'next_billing_date': subscription_data.get('next_bill_date') or subscription_data.get('billing_end_date'),
                'account_link': 'https://learn-dev.deviareacademy.africa',
                'currency': transaction_data.get('currency', 'ZAR')
            }
            
            # Render email template
            subject = "Payment received—thank you!"
            html_content = render_to_string('email/payment_success.html', context)
            text_content = render_to_string('email/payment_success.txt', context)
            
            # Send email
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                to=[user_details['email']]
            )
            email.attach_alternative(html_content, "text/html")
            
            success = email.send()
            
            # Log the email
            EmailNotificationLog.objects.create(
                customer_id=customer_id,
                transaction_id=transaction_data.get('id'),
                subscription_id=subscription_data.get('subscription_id'),
                notification_type='payment_success',
                recipient_email=user_details['email'],
                subject=subject,
                sent=bool(success),
                sent_at=timezone.now() if success else None
            )
            
            logger.info(f"Payment success email sent to {user_details['email']} for transaction {transaction_data.get('id')}")
            return bool(success)
            
        except Exception as e:
            logger.error(f"Error sending payment success email: {str(e)}")
            return False
    
    @staticmethod
    def send_order_confirmation_email(confirmed_subscriptions, customer_id, total_amount=None):
        """Send order confirmation email"""
        try:
            user_details = EmailNotificationService.get_user_details(customer_id)
            
            if not user_details['email']:
                logger.warning(f"No email found for customer {customer_id}")
                return False
            
            # Check if email already sent for this customer's confirmed subscriptions
            subscription_ids = [sub.get('id') for sub in confirmed_subscriptions]
            
            if EmailNotificationLog.objects.filter(
                customer_id=customer_id,
                subscription_id__in=subscription_ids,
                notification_type='order_confirmation',
                sent=True
            ).exists():
                logger.info(f"Order confirmation email already sent for customer {customer_id}")
                return True
            
            # Prepare order items
            order_items = []
            total_calculated = 0
            
            for sub in confirmed_subscriptions:
                display_name = sub.get('display_name', '')
                plan_name = display_name
                if '->' in display_name:
                    plan_name = display_name.split('->', 1)[1].strip()
                
                # Try to get plan details
                plan_details = None
                if sub.get('plan_id'):
                    try:
                        plan_details = PackageSubscriptionPlan.objects.get(
                            subscription_flow_plan_id=sub.get('plan_id')
                        )
                    except PackageSubscriptionPlan.DoesNotExist:
                        pass
                
                order_items.append({
                    'name': plan_name,
                    'display_name': display_name,
                    'plan_type': plan_details.plan_type if plan_details else 'Subscription'
                })
            
            # Generate order number (you can customize this)
            order_number = f"ORD-{timezone.now().strftime('%Y%m%d')}-{str(customer_id)[:8]}"
            
            # Prepare email context
            context = {
                'first_name': user_details['first_name'],
                'order_number': order_number,
                'order_items': order_items,
                'total_amount': total_amount or 'N/A',
                'order_date': timezone.now().strftime('%B %d, %Y'),
                'account_link': 'https://learn-dev.deviareacademy.africa'
            }
            
            # Render email template
            subject = "Your order has been confirmed ✅"
            html_content = render_to_string('email/order_confirmation.html', context)
            text_content = render_to_string('email/order_confirmation.txt', context)
            
            # Send email
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                to=[user_details['email']]
            )
            email.attach_alternative(html_content, "text/html")
            
            success = email.send()
            
            # Log the email for each subscription
            for sub in confirmed_subscriptions:
                EmailNotificationLog.objects.create(
                    customer_id=customer_id,
                    subscription_id=sub.get('id'),
                    notification_type='order_confirmation',
                    recipient_email=user_details['email'],
                    subject=subject,
                    sent=bool(success),
                    sent_at=timezone.now() if success else None
                )
            
            logger.info(f"Order confirmation email sent to {user_details['email']} for customer {customer_id}")
            return bool(success)
            
        except Exception as e:
            logger.error(f"Error sending order confirmation email: {str(e)}")
            return False