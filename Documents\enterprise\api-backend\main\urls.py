from django.urls import path, include
from main import tasks, views
from rest_framework.routers import De<PERSON>ultRouter
from main.viewsets import (TMForumAssessment, UserHasReport, DeregisterView, DetailOverViewReport,
                           DetailOverView, Progressreport, ExamStatus, Analytics, LabViewset, AssignProjectToUsers,
                           DownloadAnalyticsReport, DownloadProgressreport, DownloadDetailOverView, DownloadAnalyticsRprt)

router = DefaultRouter()
router.register(r"courserequest", views.CourseUserRequestAPIView, basename="courserequest")
router.register(r"courselicenceuser", views.CourseLicenseUserAPIView, basename="courselicenceuser")
router.register(r"events", views.EventsAPI, basename="events")
router.register(r"ordertransaction", views.OrderTransactionAPI, basename="ordertransaction")
router.register(r"lab", LabViewset, basename="lab")

urlpatterns = [

    # Auth Module
    path("authorize", views.Authorize.as_view()),
    #RefreshToken
    path("tokenrefresh", views.RefreshToken.as_view()),
    path("customerlogo/<str:pk>", views.CustomerLogo.as_view()),
    path("forgetpassword", views.ForgetPassword.as_view()),
    path("resetpassword", views.ResetPassword.as_view()),
    path("myprofile", views.MyProfile.as_view()),
    path("editprofile", views.EditProfile.as_view()),
    path("changepassword", views.ChangePassword.as_view()),
    path("contactus", views.ContactUs.as_view()),
    # Administrator APIs
    path("superadmindashboard", views.SuperAdminDashboard.as_view()),
    path("superadminlist", views.SuperAdminList.as_view()),
    path("superadmincreate", views.SuperAdminCreate.as_view()),
    path("superadmindetail/<str:pk>", views.SuperAdminDetail.as_view()),
    path("disableaccount", views.DisableAccount.as_view()),
    # Customer APIs
    path("gcologistdashboard", views.GCologistDashboard.as_view()),
    path("gcologistdashboard/", views.GCologistDashboard.as_view()),
    path("customeradmindashboard", views.CustomerAdminDashboard.as_view()),
    path("projectadmindashboard", views.CustomerAdminDashboard.as_view()),
    path("customerlist", views.CustomerList.as_view()),
    path("customerlistwithlicensecount", views.CustomerListForLicenseCount.as_view()),
    path("customercreate", views.CustomerCreate.as_view()),
    path("customerdetail/<str:pk>", views.CustomerDetail.as_view()),
    path("customertheme", views.GetCustomerThemeView.as_view()),
    path("customertheme/<str:pk>", views.CustomerThemeView.as_view()),
    # User APIs
    path("userdashboard", views.UserDashboard.as_view()),
    path("userlist", views.UserList.as_view()),
    path("userlist/all", views.UserListAll.as_view()),
    path("usermonthcount", views.UserAnalytic.as_view()),
    path("usersignup", views.UserSignup.as_view()),
    path("usercreate", views.UserCreate.as_view()),
    path("usercreatemultiple", views.UserCreateMultiple.as_view()),
    path("userupdate", views.UserUpdate.as_view()),
    path("userdetail/<str:pk>", views.UserDetail.as_view()),
    path("userdetail/", views.UserCreate.as_view()),
    path("userdetailadmin/<str:pk>", views.UserDetailforAdmin.as_view()),
    path("bulkuploadtemplate", views.BulkUploadTemplate.as_view()),
    path("bulkstorage", views.BulkStorage.as_view()),
    path(r'assign_project_to_users', AssignProjectToUsers.as_view()),
    path(r"get_all_users_email", views.GetUsersEmailsAPI.as_view()),
    # Course APIs
    path("courselist", views.CourseList.as_view()),
    # path("categorylist", views.CategoryDropDown.as_view()),
    path("coursecreate", views.CourseCreate.as_view()),
    path("coursedetail/<str:pk>", views.CourseDetail.as_view()),
    path("coursedetailecom/<str:pk>", views.CourseDetailForEcom.as_view()),
    path("simplilearncourses", views.SimpliLearnCourses.as_view()),
    path("coursedata", views.CourseData.as_view()),
    path("bulkuserupload", views.BulkUserUpload.as_view()),
    path("popularcoursegraph", views.PopularCourseGraph.as_view()),
    # Event APIs
    # path("events", views.EventsAPI.as_view()),
    path("event", views.EventCreateAPI.as_view()),
    path("event/<str:pk>", views.EventAPI.as_view()),
    # path("microsoftcourses", views.MicrosoftCourses.as_view()),
    path("addusertocourse", views.CourseUserAssigmentAPIView.as_view({'post': 'create'})),
    path("gotocourse/<str:pk>", views.UserGoToCourseAPIView.as_view()),
    path("userstatusincourse", views.UserStatusInCourseAPIView.as_view()),
    # Drop-Downs
    path("countrylist", views.countrylist),
    path("customerdropdown", views.CustomerDropDown.as_view()),
    path("customeradmindropdown", views.CustomerAdminDropDown.as_view()),
    path("projectadmindropdown", views.ProjectAdminDropDown.as_view()),
    path("coursedropdown", views.CourseDropDown.as_view()),
    path("projectdropdown", views.ProjectDropDown.as_view()),
    path("assessmentdropdown", views.AssessmentDropDown.as_view()),
    path("apprenticeshipdropdown", views.ApprenticeshipDropDown.as_view()),

    # Customization URLS'
    path("customization", views.ReportCustomizationAPIView.as_view()),

    # TMForumconst
    path(r'tmforum/', include("main.tmforum_assessment.urls")),
    path(r'tmforum/struct', TMForumAssessment.as_view()),

    path(r"user_has_reports/", UserHasReport.as_view()),
    # PDF Reports 

    path(r"user_progress", views.user_progress),
    path(r"user_progress/pdf", views.UserProgressReport.as_view()),
    path(r"digital_readiness", views.digital_readiness),
    path(r"digital_readiness/pdf", views.CustomerDigitalReadinessReport.as_view()),
    path(r"user_learning_path/pdf", views.UserLearningPathReport.as_view()),
    # VGA
    path("vga", views.VGA.as_view()),
    path("addbranch", tasks.allocate_userTobranchlms),   

    path(r'getcompanydetail', views.GetCompanyByID.as_view()),
    # Report routes
  
    path(r'detailedoverview', DetailOverView.as_view()),
    path(r'progressreport', Progressreport.as_view()),
    path(r'deregister', DeregisterView.as_view()),
    path(r'examstatus', ExamStatus.as_view()),
    path(r'detailedanalytics', Analytics.as_view()),
    #path(r'download_analytics_report', DownloadAnalyticsReport.as_view()),
    path(r'download_analytics_report', DownloadAnalyticsRprt.as_view()),
    path(r'download_overview_report', DownloadDetailOverView.as_view()),
    path(r'download_progress_report', DownloadProgressreport.as_view()),
    path(r'reportmail', DetailOverViewReport.as_view()),
    path(r'rdaurl', views.DigitalReadiness.as_view()),
    path(r'airdaurl', views.AIReadinessAssessment.as_view()),
    path(r'api/user-assigned-assessments/', views.UserAssignedAssessments.as_view(), name='user_assigned_assessments'),
    
    # all platform admin API's
    path(r'userblock', views.UserManagement.as_view()),
    path(r'addnotification', views.NotificationTypeAdd.as_view()),
    path(r'updatepermissions', views.KeyClockAdmin.as_view()),
    path(r'product', views.ProductView.as_view()),
    path(r'notificationtemplate', views.NotificationTemplateView.as_view()),
    path(r"product/<str:pk>", views.ProductUpdateView.as_view()),
    path(r"notificationtemplate/<str:pk>", views.TemplateUpdate.as_view()),
    path(r"trigger", views.NotificationTrigger.as_view()),
    path(r"assign_customer_license", views.AssignCustomerLicense.as_view()),
    path(r"project_summary", views.ProjectSummary.as_view()),
    path(r"customer_admin_user", views.CustomerAdminUser.as_view()),
    path(r"userbyproject", views.UsersByProject.as_view()),
    path("testemail", views.TestSMTP.as_view()),
    path(r"authprovider", views.SsoAuth.as_view()),
    path(r"drauserdata", views.CloseGap.as_view()),
    path(r"drareportdata", views.DRAReport.as_view()),
    path(r"download_drareportdata", views.DRAReportDownload.as_view()),
    path(r"drausers", views.DRAReport.as_view()),
    path(r"authcourseprovider", views.SSOCourse.as_view()),
    path(r"ssoreport", views.SSOLicense.as_view()),
    path(r"thankyouemail", views.ThankyouEmail.as_view()),
    path(r"fetch_sparrow", views.SurveysparrowManual.as_view()),

    #Payment api 
    path(r"payment_callback", views.PaymentCallback.as_view(), name='payment_callback'),
    path(r"payment_status_check", views.CheckPaymentStatus.as_view(), name='payment_status'),
    path(r"payment_callback_url", views.PaymentCallbackUrl.as_view(), name='payment_callback'),
    path(r"project_admin_users/<str:pk>", views.ProjectAdminUsers.as_view()),
    path(r"labs_mapped_with_course", views.LabsMappedWithCourseAPIView.as_view()),


    #Launch Lab
    path(r"launch_labs", views.LaunchLabAPIView.as_view()),
    # Power BI
    path("powerbi_token",views.MicrosoftTokenView.as_view()),
    path("powerbi_report", views.PowerBIApiView.as_view()),

    # Digiatal Talent Assessment APIs
    path('user-survey-averages', views.UserSurveyAveragesView.as_view(), name='user-survey-averages'),
    path('company-survey-averages', views.CompanyProjectSurveyAveragesView.as_view(), name='company-survey-averages'),
    path('download-user-survey-averages', views.UserSurveyAveragesDownload.as_view(), name='download-user-survey-averages'),

    #Others
    path(r"companytalentusers", views.CompanyTalentUsers.as_view(), name='company-talent-users'),
    path(r"companyallusers", views.CompanyAllUsers.as_view(), name='company-all-users'),

    # AI Maturity Model assessment APIs
    path(r"ai-assessment", views.AISurveyAveragesView.as_view(), name='ai-assessment'),
    path(r'ai-company-survey-averages', views.AICompanyProjectSurveyAveragesView.as_view(), name='ai-company-survey-averages'),
    path(r'ai-download-user-survey-averages', views.AIUserSurveyAveragesDownload.as_view(), name='ai-download-user-survey-averages'),

    # group (Composer)
    path(r"composer", views.ComposerListCreateAPIView.as_view(), name='composer-list-create'),
    path(r"composer-dropdown", views.ComposerDropdownAPIView.as_view(), name='composer-dropdown'),
    path(r"composer/<int:pk>", views.ComposerListCreateAPIView.as_view(), name='composer-detail'),
    path(r"composer-assign", views.AssignComposerToUserAPIView.as_view(), name='assign-composer-to-user'),
    path(r"user-composers", views.UserComposersAPIView.as_view(), name='user-composers'),
    
    # E-Commerce
    path(r"coursedropdownsandlist", views.CourseDropdownsAndListAPIView.as_view(), name='coursedropdownsandlist'),
    path(r"coursedetails/<str:course_uuid>", views.CourseDetailsAPIView.as_view(), name='coursedetails'),
    path(r'packages', views.PackageSubscriptionListAPIView.as_view(), name='packages-subscription-list'),

    
    # SubscriptionFlow Webhook
    path(r"subscriptionflow_webhook", views.SubscriptionFlowWebhook.as_view(), name="subscriptionflow_webhook"),
    path(r"orderSubscription/<uuid:customer_id>/", views.OrderConfirmationAPIView.as_view(), name="orderSubscription"),
    path(r"Billing/<uuid:customer_id>/", views.BillingDetailsAPIView.as_view(), name="Billing"),
    path(r"CustomerCourses/<uuid:customer_id>/", views.CustomerCoursesAPIView.as_view(), name="CustomerCourses"),
    path(r"AssignCourseToUser/", views.AssignCourseToUserAPIView.as_view(), name="AssignCourseToUser"),
    path(r"CancelSubscription/<str:subscription_id>/", views.CancelSubscriptionAPIView.as_view(), name="CancelSubscription"),
    path(r"testEmail/<uuid:customer_id>/", views.TestEmailAPIView.as_view(), name="testEmail"),
    path(r'lastLaunchCourse/', views.LastLaunchedCourseAPIView.as_view(), name='lastLaunchCourse'),
    path(r'invoice/<str:invoice_id>/', views.InvoiceDetailAPIView.as_view(), name='invoice-detail'),
    path(r"myplayersusersignup", views.MyPlayersSignup.as_view(), name = "myplayersusersignup"),


] + router.urls


