0000000000000000000000000000000000000000 5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d <PERSON><PERSON><PERSON> <<EMAIL>> 1752582117 +0530	branch: Created from HEAD
5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d e27ee115eec7e7492acb5ab0cd7a958ca0492d3a <PERSON><PERSON><PERSON> <<EMAIL>> 1752582252 +0530	commit: till fetch list of category done
e27ee115eec7e7492acb5ab0cd7a958ca0492d3a 42fcd355ed024b171432c6bc24a6ad3722a6db05 <PERSON><PERSON><PERSON> <<EMAIL>> 1752654137 +0530	commit: get category by id done
42fcd355ed024b171432c6bc24a6ad3722a6db05 567f882d73e7ffb8167179f99972f9b41bc63ac<PERSON> <PERSON><PERSON> <<EMAIL>> 1753447601 +0530	commit: course models update for ecommerce
567f882d73e7ffb8167179f99972f9b41bc63ac5 9493abbcd314cb5ef66cefb043ca9161ea158f60 Rahul Kumar <<EMAIL>> 1753724192 +0530	pull origin staging: Fast-forward
9493abbcd314cb5ef66cefb043ca9161ea158f60 d0ba07d4b20a8aa66d19aa29377416951ade89cb Rahul Kumar <<EMAIL>> 1753724544 +0530	pull origin staging: Fast-forward
d0ba07d4b20a8aa66d19aa29377416951ade89cb 5fc56cf47d05292990c80f6348998c1b6288d8ef Rahul Kumar <<EMAIL>> 1753773795 +0530	pull origin staging: Fast-forward
5fc56cf47d05292990c80f6348998c1b6288d8ef 5e1e7579375400742137c3d0d60715d23684f46a Rahul Kumar <<EMAIL>> 1755085452 +0530	commit: inintial setup and API created
5e1e7579375400742137c3d0d60715d23684f46a 54b498986198999dbc819a1251adf57a956e32e3 Rahul Kumar <<EMAIL>> 1755602777 +0530	pull origin staging: Fast-forward
54b498986198999dbc819a1251adf57a956e32e3 73a8f49c57a22c4313b23e45c0a476a44d75d51f Rahul Kumar <<EMAIL>> 1755602841 +0530	commit: db synced & celery part done
73a8f49c57a22c4313b23e45c0a476a44d75d51f 667ed7b94534753a4f833df7e54ef0abcd69ff02 Rahul Kumar <<EMAIL>> 1755755221 +0530	commit: Add search functionality to CourseDropdownsAndListAPIView
667ed7b94534753a4f833df7e54ef0abcd69ff02 24a45009cda77c1c2c39c557a41c08cab8e9ec26 Rahul Kumar <<EMAIL>> 1755759530 +0530	pull origin staging: Fast-forward
24a45009cda77c1c2c39c557a41c08cab8e9ec26 00193cff99a09168159bc8f07aba683e02f3201c Rahul Kumar <<EMAIL>> 1755858024 +0530	commit: update course detailed response
00193cff99a09168159bc8f07aba683e02f3201c 03ce88eaec30adc3a958a1631d993b7505fe453d Rahul Kumar <<EMAIL>> 1755865833 +0530	commit: updated course detailed responses
03ce88eaec30adc3a958a1631d993b7505fe453d 9b87415b96a9212fbb1ad4b5bb4b765f934153ce Rahul Kumar <<EMAIL>> 1756102250 +0530	commit: updated course detailed responses...
9b87415b96a9212fbb1ad4b5bb4b765f934153ce 3e4917fb2503172a4ea54aaa14af06ef312b8633 Rahul Kumar <<EMAIL>> 1756106889 +0530	commit: updated course detailed repsonse remove , add fixed pattern
3e4917fb2503172a4ea54aaa14af06ef312b8633 bb30cb571ac0f231be96d4a6f0e25d106141f4d1 Rahul Kumar <<EMAIL>> 1756108560 +0530	pull origin staging: Fast-forward
bb30cb571ac0f231be96d4a6f0e25d106141f4d1 9bb190a327378e9a3e22223d3a9041ff3a2158ea Rahul Kumar <<EMAIL>> 1756183833 +0530	commit: updated course detailed repsonse select top3
9bb190a327378e9a3e22223d3a9041ff3a2158ea 86d2a7755b70a4e3a89a510105207452d35cdab7 Rahul Kumar <<EMAIL>> 1756294062 +0530	commit: create SubscriptionFlowWebhook api
86d2a7755b70a4e3a89a510105207452d35cdab7 3bff5e73481f8ebda2697043e8fa416d3da3042b Rahul Kumar <<EMAIL>> 1756298636 +0530	commit: update bulkupload API
3bff5e73481f8ebda2697043e8fa416d3da3042b 1e73d4ab2f0dfe2f958d178ccfd8a7fd8fb3e4db Rahul Kumar <<EMAIL>> 1756314563 +0530	pull origin staging: Fast-forward
1e73d4ab2f0dfe2f958d178ccfd8a7fd8fb3e4db ba0b17010ef501d86c5edfc36adf9f32966a8190 Rahul Kumar <<EMAIL>> 1756359564 +0530	commit: update wehook api payload
ba0b17010ef501d86c5edfc36adf9f32966a8190 703a0a33f6ad10d0113875ed3df68aeb024f0789 Rahul Kumar <<EMAIL>> 1756446242 +0530	commit: Add configurable processing delays for SubscriptionFlow webhook handling
703a0a33f6ad10d0113875ed3df68aeb024f0789 372e2fb05d54107ef355401994df5a14e877d5ba Rahul Kumar <<EMAIL>> 1756456669 +0530	commit: Add configurable processing delays for SubscriptionFlow webhook handling...
372e2fb05d54107ef355401994df5a14e877d5ba 1f55eaebba3d8df9ef96bcce6a5936eb580cf09f Rahul Kumar <<EMAIL>> 1756465158 +0530	commit: update delays for SubscriptionFlow webhook handling...
1f55eaebba3d8df9ef96bcce6a5936eb580cf09f c5c2726e5d748f2274c9d1ce5cbecffb8772f412 Rahul Kumar <<EMAIL>> 1756468886 +0530	commit: Enhance SubscriptionFlowWebhook to always fetch complete invoice details and include transaction data
c5c2726e5d748f2274c9d1ce5cbecffb8772f412 38d96a990d59cb8b217363e8b45b3d322d1d9d24 Rahul Kumar <<EMAIL>> 1756471715 +0530	commit: Enhance SubscriptionFlowWebhook to log additional invoice and transaction details, add processing delays, and ensure complete data fetching from AP
38d96a990d59cb8b217363e8b45b3d322d1d9d24 69ba99d02b0b580d79d0f621a9d0428bccb26bbd Rahul Kumar <<EMAIL>> 1756555076 +0530	commit: Enhance SubscriptionFlowWebhook by subscription event
69ba99d02b0b580d79d0f621a9d0428bccb26bbd b771e2db4accda4a96b3b9bb90ce0897bfd39cef Rahul Kumar <<EMAIL>> 1756704172 +0530	commit: add delay time
b771e2db4accda4a96b3b9bb90ce0897bfd39cef 60cef05ad8f77a577081b312d41991ca546d1943 Rahul Kumar <<EMAIL>> 1756874767 +0530	commit: Enhance SubscriptionFlowWebhook to handle invoice paid events
60cef05ad8f77a577081b312d41991ca546d1943 4159c358c3d606fab201af2e330cd00803fd4ef8 Rahul Kumar <<EMAIL>> 1756881195 +0530	commit: update CourseDetailsAPIView
4159c358c3d606fab201af2e330cd00803fd4ef8 06734e72cc301de883259b2e771f6e79866def3a Rahul Kumar <<EMAIL>> 1756900844 +0530	commit: Update course plan IDs and enhance HTML to text conversion in CourseDetailsAPIView
06734e72cc301de883259b2e771f6e79866def3a 473608faa3f1313cee6a4187523ced59e785d484 Rahul Kumar <<EMAIL>> 1757580286 +0530	commit: Enhance API endpoints for subscription flow: add order confirmation and billing details views, clean HTML tags in course descriptions, and improve error handling in progress fetching.
473608faa3f1313cee6a4187523ced59e785d484 38ea36156885e4d8ee43916579929c8228c35b0b Rahul Kumar <<EMAIL>> 1757593432 +0530	commit: Refactor HTML cleaning in CourseCatalog and RegisteredCourses: enhance clean_html_tags method to handle entities, normalize whitespace, and limit to 60 words.
38ea36156885e4d8ee43916579929c8228c35b0b 1c0bd0e7a116d7a3a437e446c9d3d70b471f59ed Rahul Kumar <<EMAIL>> 1757612845 +0530	commit: Fix Unicode encoding issues in HTML cleaning for CourseCatalog and RegisteredCourses
1c0bd0e7a116d7a3a437e446c9d3d70b471f59ed 50a582558f41d5a22a7cec947908b418456848d0 Rahul Kumar <<EMAIL>> 1757614789 +0530	commit: Fix Unicode encoding issues in CourseCatalog and RegisteredCourses: remove stray characters and leftover fragments
50a582558f41d5a22a7cec947908b418456848d0 fbdcd280a256966867120b76e18d82e8a30c2afa Rahul Kumar <<EMAIL>> 1757615713 +0530	pull origin staging: Fast-forward
fbdcd280a256966867120b76e18d82e8a30c2afa 2d7e0c35e2a18a75d945a6b279627f80edc04af3 Rahul Kumar <<EMAIL>> 1757616088 +0530	commit: Fix Unicode encoding issues
2d7e0c35e2a18a75d945a6b279627f80edc04af3 ebbdf536c44be4b3f3b469c3d5a7555bcbfc49f0 Rahul Kumar <<EMAIL>> 1757653786 +0530	pull origin staging: Fast-forward
ebbdf536c44be4b3f3b469c3d5a7555bcbfc49f0 5edbd2f85ecf3f3b2fe5845fc5cf07f0e1b963e5 Rahul Kumar <<EMAIL>> ********** +0530	commit: Refactor HTML cleaning
5edbd2f85ecf3f3b2fe5845fc5cf07f0e1b963e5 a5e27ff8f72b08417010b93c1839714d4868c88c Rahul Kumar <<EMAIL>> ********** +0530	pull origin staging: Fast-forward
a5e27ff8f72b08417010b93c1839714d4868c88c 586c4b2ef2e85f4e92b9ab912720df3152de537a Rahul Kumar <<EMAIL>> ********** +0530	commit: Enhance CSV upload process: update CourseLicenseUser with self-learning completion and improve error handling; add HTML cleaning for course descriptions in API response.
586c4b2ef2e85f4e92b9ab912720df3152de537a 2ff73072cb9ee7047478eebe779239dccbd0e60b Rahul Kumar <<EMAIL>> ********** +0530	pull origin staging: Fast-forward
2ff73072cb9ee7047478eebe779239dccbd0e60b 993855b66dea7c4b379bcfccfa9546cc7a9fe265 Rahul Kumar <<EMAIL>> ********** +0530	commit: Add CustomerCourses API endpoint to fetch courses based on subscription package
993855b66dea7c4b379bcfccfa9546cc7a9fe265 3c38a34e024ce02ffd28ca48082f1a0450b87545 Rahul Kumar <<EMAIL>> ********** +0530	commit: Refactor text cleaning to complete snetence
3c38a34e024ce02ffd28ca48082f1a0450b87545 ded868a7c4b2f1a1d22c33dbb8b15234e34ad6e7 Rahul Kumar <<EMAIL>> ********** +0530	commit: Add AssignCourseToUser API endpoint for course assignment to users
ded868a7c4b2f1a1d22c33dbb8b15234e34ad6e7 73ebf303c98418cfc0508035b897670a1393c410 Rahul Kumar <<EMAIL>> ********** +0530	pull origin staging: Fast-forward
73ebf303c98418cfc0508035b897670a1393c410 864cdb7f9da380b75ac42b48a1cc6f1a82a29546 Rahul Kumar <<EMAIL>> 1758019803 +0530	commit: Add course access check for user in CourseDetailsAPIView
864cdb7f9da380b75ac42b48a1cc6f1a82a29546 c029d63c3c1ac9806e9c43711a6a3b1ab0bafe0d Rahul Kumar <<EMAIL>> 1758020307 +0530	pull origin staging: Fast-forward
c029d63c3c1ac9806e9c43711a6a3b1ab0bafe0d 608c3325610a14ecf325a61da9895fe74d0e91ac Rahul Kumar <<EMAIL>> 1758093811 +0530	commit: Update CourseManager to exclude Xpert Skills courses in queryset
608c3325610a14ecf325a61da9895fe74d0e91ac 9eaad00e41869b7a35d395ec059f149b08b30df6 Rahul Kumar <<EMAIL>> 1758096803 +0530	commit: Add task to apply grace period to subscriptions and schedule it for daily execution
9eaad00e41869b7a35d395ec059f149b08b30df6 9b32ae42d82a014646878f6d3db15aabf45fcd9f Rahul Kumar <<EMAIL>> 1758099555 +0530	pull origin staging: Fast-forward
9b32ae42d82a014646878f6d3db15aabf45fcd9f 314cee08d29dc7379b947d1042716b17e9ee5d95 Rahul Kumar <<EMAIL>> 1758175605 +0530	commit: Add CancelSubscription API endpoint and enhance subscription data handling
314cee08d29dc7379b947d1042716b17e9ee5d95 485966ded401c4e2e8e27248bba0c49f0c2d998b Rahul Kumar <<EMAIL>> 1758178142 +0530	pull origin staging: Fast-forward
485966ded401c4e2e8e27248bba0c49f0c2d998b 1d72788f108350b7e211af1dd49b5503505f852c Rahul Kumar <<EMAIL>> 1758180480 +0530	commit: Add LastLaunchedCourse API endpoint to retrieve the last accessed course for a user
1d72788f108350b7e211af1dd49b5503505f852c 2fbed382ac4ac3bf47c252579be91e52d9168b27 Rahul Kumar <<EMAIL>> ********** +0530	commit: Fix launch URL in LastLaunchedCourseAPIView to use the correct course link
2fbed382ac4ac3bf47c252579be91e52d9168b27 133931ec3513157640e428134a152b65ea63d02c Rahul Kumar <<EMAIL>> ********** +0530	commit: Enhance user access validation in CourseDetailsAPIView to check for active subscriptions and legacy licenses
133931ec3513157640e428134a152b65ea63d02c 7c9e8325c2c78a2d7c0f0847874226a50d48dc44 Rahul Kumar <<EMAIL>> ********** +0530	commit: Refactor BillingDetailsAPIView to retrieve customer details from SubscriptionFlow API instead of local database
7c9e8325c2c78a2d7c0f0847874226a50d48dc44 b72f729f3f0ca0572be15cf5ff2b1505320945cc Rahul Kumar <<EMAIL>> ********** +0530	pull origin staging: Fast-forward
b72f729f3f0ca0572be15cf5ff2b1505320945cc 107dd52d187eca8736d39ed193d33c1ad6fcacdf Rahul Kumar <<EMAIL>> ********** +0530	commit: Add CORS headers to CustomerCoursesAPIView for improved API accessibility
107dd52d187eca8736d39ed193d33c1ad6fcacdf 4a1ec60998927cf086a79ab9d5757199b3f8c3b9 Rahul Kumar <<EMAIL>> ********** +0530	commit: Add CORS configuration to settings for development and production environments
4a1ec60998927cf086a79ab9d5757199b3f8c3b9 cd502338988fd70b233e57ff0c77f0fd34ade0dc Rahul Kumar <<EMAIL>> ********** +0530	commit: Update CORS configuration to allow credentials and all headers for improved API accessibility
cd502338988fd70b233e57ff0c77f0fd34ade0dc 6aab3631adfb86ada8a44bc8224352269ba97a0e Rahul Kumar <<EMAIL>> 1758275083 +0530	commit: add cors middleware at top
6aab3631adfb86ada8a44bc8224352269ba97a0e c6513fa75a22773d5fc7d12717f557d4355fb18e Rahul Kumar <<EMAIL>> 1758276797 +0530	pull origin staging: Fast-forward
c6513fa75a22773d5fc7d12717f557d4355fb18e 59d767815ee831e9fec0d4d3cde7dc8f95e5d955 Rahul Kumar <<EMAIL>> 1758279441 +0530	commit: Enhance CORS configuration to support additional origins, headers, and methods for improved API accessibility
59d767815ee831e9fec0d4d3cde7dc8f95e5d955 af05974799f2f3c743cfe0ae58fd4010c7c2edfc Rahul Kumar <<EMAIL>> 1758286875 +0530	commit: Refactor CORS settings: remove specific origins and headers, add CORS headers to all responses in CustomerCoursesAPIView
af05974799f2f3c743cfe0ae58fd4010c7c2edfc 2cf3a041b489ed12a8991a8fae75b3d5db9a7465 Rahul Kumar <<EMAIL>> 1758434308 +0530	pull origin staging: Fast-forward
2cf3a041b489ed12a8991a8fae75b3d5db9a7465 ce9539e282ae94253d9b2412c7b83e00661de75f Rahul Kumar <<EMAIL>> 1758441251 +0530	commit: Update course plans and VAT rates: rename plans for clarity, add Freemium option, and enhance regex patterns for better text cleaning
ce9539e282ae94253d9b2412c7b83e00661de75f 5428d04e671fee24c16e71be0cbe7d79162f19bc Rahul Kumar <<EMAIL>> 1758442899 +0530	pull origin staging: Fast-forward
5428d04e671fee24c16e71be0cbe7d79162f19bc b0091c42d4a6d7fd4e2fdcb44ebcb609b09cf5f4 Rahul Kumar <<EMAIL>> 1758514948 +0530	commit: Update subscription plan names: rename 'Free - 30day trial' to 'Freemium' and enhance confirmed subscriptions handling in OrderConfirmationAPIView
b0091c42d4a6d7fd4e2fdcb44ebcb609b09cf5f4 d1882be923cdbae9aeaac3cbac8047057cf25324 Rahul Kumar <<EMAIL>> 1758517789 +0530	commit: add invoice id in billing
d1882be923cdbae9aeaac3cbac8047057cf25324 8199e93fedca26d92009e189268879abdc390d7a Rahul Kumar <<EMAIL>> 1758527754 +0530	commit (merge): add endpoint to fetch invoice detail
8199e93fedca26d92009e189268879abdc390d7a ca6bdf910ba2e25cdf0980bf55b2b61c7889da3f Rahul Kumar <<EMAIL>> 1758528701 +0530	commit (merge): Merge branch 'staging' into EcommerceTemp
ca6bdf910ba2e25cdf0980bf55b2b61c7889da3f ec5ea6f8fef8825eca61bd49ec98a03ff30646e2 Rahul Kumar <<EMAIL>> 1758529697 +0530	commit (merge): Merge branch 'staging' of https://bitbucket.org/deviare/api-backend into EcommerceTemp
ec5ea6f8fef8825eca61bd49ec98a03ff30646e2 9ff688e584f3ca3139b657e54636a3ba8a5bfb4e Rahul Kumar <<EMAIL>> 1758694991 +0530	commit: create new signup for myplayers
9ff688e584f3ca3139b657e54636a3ba8a5bfb4e a3bb2edb6403aca6c2cbcb1460a77e593abaf767 Rahul Kumar <<EMAIL>> 1758708784 +0530	commit: add email notification log
